// Ticket Generator for AIpariat
// Integrates with real AI API for betting slip generation

export interface Match {
  match: string;
  date: string;
  sport: string;
  bet_type: string;
  selection: string;
  odds: number;
  league: string;
}

export interface Ticket {
  id: string;
  type: string;
  matches: Match[];
  totalOdds: number;
  description: string;
  confidence: string;
  explanation?: string;
  recommended_stake?: number;
  potential_win?: number;
  risk_level?: string;
}

// API Configuration
const API_BASE_URL = 'https://aipariatagent-1914c9cd53a9.herokuapp.com';

// API Response interfaces
interface ApiTaskResponse {
  task_id: string;
  status: string;
  message: string;
}

interface ApiStatusResponse {
  task_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress?: number;
  created_at?: string;
  completed_at?: string;
}

interface ApiBettingSlip {
  matches: {
    match: string;
    date: string;
    sport: string;
    bet_type: string;
    selection: string;
    odds: number;
    league: string;
  }[];
  total_odds: number;
  recommended_stake?: number;
  potential_win?: number;
  risk_level?: string;
  confidence: string;
  explanation?: string;
}

interface ApiResultResponse {
  task_id: string;
  status: string;
  query: string;
  betting_slip: ApiBettingSlip;
  grounding_info?: {
    has_grounding: boolean;
    web_search_queries: string[];
    sources: { title: string; uri: string }[];
  };
}

// API Integration Functions
async function createBettingSlipTask(query: string, options: {
  max_odds?: number;
  budget?: number;
  sport?: string;
} = {}): Promise<string> {
  try {
    const response = await fetch(`${API_BASE_URL}/betting-slip/create`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        ...options
      }),
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status}`);
    }

    const data: ApiTaskResponse = await response.json();
    return data.task_id;
  } catch (error) {
    console.error('Error creating betting slip task:', error);
    throw error;
  }
}

async function pollTaskStatus(taskId: string): Promise<ApiResultResponse> {
  const maxAttempts = 30; // 30 attempts * 2 seconds = 60 seconds max
  let attempts = 0;

  while (attempts < maxAttempts) {
    try {
      const response = await fetch(`${API_BASE_URL}/betting-slip/status/${taskId}`);

      if (!response.ok) {
        throw new Error(`Status check failed: ${response.status}`);
      }

      const statusData: ApiStatusResponse = await response.json();

      if (statusData.status === 'completed') {
        // Add a small delay to ensure result is ready
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Try to get the result with retry logic
        let resultAttempts = 0;
        const maxResultAttempts = 3;

        while (resultAttempts < maxResultAttempts) {
          try {
            const resultResponse = await fetch(`${API_BASE_URL}/betting-slip/result/${taskId}`);

            if (resultResponse.ok) {
              return await resultResponse.json();
            } else if (resultResponse.status === 404) {
              console.warn(`Result not ready yet, attempt ${resultAttempts + 1}/${maxResultAttempts}`);
              if (resultAttempts < maxResultAttempts - 1) {
                await new Promise(resolve => setTimeout(resolve, 2000));
                resultAttempts++;
                continue;
              }
            }

            throw new Error(`Result fetch failed: ${resultResponse.status}`);
          } catch (resultError) {
            if (resultAttempts >= maxResultAttempts - 1) {
              throw resultError;
            }
            await new Promise(resolve => setTimeout(resolve, 2000));
            resultAttempts++;
          }
        }
      } else if (statusData.status === 'failed') {
        throw new Error('Betting slip generation failed');
      }

      // Wait 2 seconds before next poll
      await new Promise(resolve => setTimeout(resolve, 2000));
      attempts++;
    } catch (error) {
      console.error('Error polling task status:', error);
      if (attempts >= maxAttempts - 1) {
        throw error;
      }
      await new Promise(resolve => setTimeout(resolve, 2000));
      attempts++;
    }
  }

  throw new Error('Task timeout - betting slip generation took too long');
}

function convertApiResponseToTicket(apiResponse: ApiResultResponse, type: string): Ticket {
  const bettingSlip = apiResponse.betting_slip;

  return {
    id: Date.now().toString(),
    type,
    matches: bettingSlip.matches,
    totalOdds: bettingSlip.total_odds,
    description: getDescriptionForType(type),
    confidence: bettingSlip.confidence,
    explanation: bettingSlip.explanation,
    recommended_stake: bettingSlip.recommended_stake,
    potential_win: bettingSlip.potential_win,
    risk_level: bettingSlip.risk_level
  };
}

function getDescriptionForType(type: string): string {
  switch (type) {
    case "curajos":
      return "🔥 **Cel mai curajos bilet** - Cote mari pentru câștiguri spectaculoase!";
    case "sigur":
      return "🛡️ **Cel mai sigur bilet** - Selecții cu șanse mari de câștig!";
    case "ziua":
      return "⭐ **Recomandarea zilei** - Selecția AI pentru astăzi!";
    case "saptamana":
      return "👑 **Recomandarea săptămânii** - Biletul premium al săptămânii!";
    default:
      return "🎯 **Bilet personalizat** - Generat special pentru tine!";
  }
}

function getQueryForType(type: string): string {
  switch (type) {
    case "curajos":
      return "Vreau un bilet curajos cu cote mari și risc ridicat pentru meciuri din următoarele zile";
    case "sigur":
      return "Vreau un bilet sigur cu cote mici și șanse mari de câștig pentru meciuri din următoarele zile";
    case "ziua":
      return "Vreau recomandarea zilei pentru pariuri sportive cu analiză AI pentru astăzi";
    case "saptamana":
      return "Vreau recomandarea săptămânii pentru pariuri sportive, biletul premium al săptămânii";
    default:
      return "Vreau un bilet pentru pariuri sportive cu analiză AI";
  }
}

// Generate predefined tickets using real API
export async function generatePredefinedTicket(type: string): Promise<Ticket> {
  try {
    const query = getQueryForType(type);
    const options: { max_odds?: number; budget?: number; sport?: string } = {};

    // Set specific options based on type
    switch (type) {
      case "curajos":
        options.max_odds = 10.0; // Higher odds for brave bets
        break;
      case "sigur":
        options.max_odds = 3.0; // Lower odds for safe bets
        break;
      case "ziua":
        options.sport = "fotbal";
        break;
      case "saptamana":
        options.budget = 100.0;
        break;
    }

    console.log(`🎯 Generating ${type} ticket with query: "${query}" and options:`, options);

    const taskId = await createBettingSlipTask(query, options);
    console.log(`✅ Task created with ID: ${taskId}`);

    const apiResponse = await pollTaskStatus(taskId);
    console.log(`✅ API response received for ${type} ticket`);

    return convertApiResponseToTicket(apiResponse, type);
  } catch (error) {
    console.error(`❌ Error generating ${type} ticket:`, error);
    // Return fallback ticket in case of API failure
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return createFallbackTicket(type, undefined, errorMessage);
  }
}

// Generate ticket based on user query using real API
export async function generateChatTicket(query: string): Promise<Ticket> {
  try {
    console.log(`💬 Generating chat ticket with query: "${query}"`);

    const taskId = await createBettingSlipTask(query);
    console.log(`✅ Chat task created with ID: ${taskId}`);

    const apiResponse = await pollTaskStatus(taskId);
    console.log(`✅ Chat API response received`);

    return convertApiResponseToTicket(apiResponse, "chat");
  } catch (error) {
    console.error('❌ Error generating chat ticket:', error);
    // Return fallback ticket in case of API failure
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    return createFallbackTicket("chat", query, errorMessage);
  }
}

// Fallback function for when API fails
function createFallbackTicket(type: string, query?: string, errorMessage?: string): Ticket {
  const fallbackMatches: Match[] = [
    {
      match: "FCSB vs CFR Cluj",
      date: "2025-07-10 20:00",
      sport: "fotbal",
      bet_type: "1X2",
      selection: "1",
      odds: 2.10,
      league: "Liga 1 Romania"
    },
    {
      match: "Real Madrid vs Barcelona",
      date: "2025-07-11 21:00",
      sport: "fotbal",
      bet_type: "1X2",
      selection: "X",
      odds: 3.20,
      league: "La Liga"
    },
    {
      match: "Manchester City vs Liverpool",
      date: "2025-07-12 17:30",
      sport: "fotbal",
      bet_type: "Over/Under",
      selection: "Over 2.5",
      odds: 1.85,
      league: "Premier League"
    }
  ];

  const totalOdds = fallbackMatches.reduce((acc, match) => acc * match.odds, 1);

  const explanation = errorMessage
    ? `Bilet generat cu date simulate. Eroare API: ${errorMessage}. Încearcă din nou în câteva momente.`
    : "Bilet generat cu date simulate din cauza unei probleme temporare cu API-ul. Încearcă din nou în câteva momente.";

  return {
    id: Date.now().toString(),
    type,
    matches: fallbackMatches,
    totalOdds: parseFloat(totalOdds.toFixed(2)),
    description: query ? "🎯 **Bilet personalizat** - Generat special pentru tine!" : getDescriptionForType(type),
    confidence: "mediu",
    explanation,
    risk_level: "mediu"
  };
}

// Format ticket for display
export function formatTicket(ticket: Ticket): string {
  let formatted = `${ticket.description}\n\n`;

  ticket.matches.forEach((match, index) => {
    // Parse match string to get team names
    const teams = match.match.split(' vs ');
    const team1 = teams[0] || match.match;
    const team2 = teams[1] || '';

    formatted += `${index + 1}. **${team1}**${team2 ? ` vs **${team2}**` : ''}\n`;
    formatted += `   📍 ${match.league}`;
    if (match.date) {
      const date = new Date(match.date);
      const timeStr = date.toLocaleTimeString('ro-RO', { hour: '2-digit', minute: '2-digit' });
      formatted += ` | ⏰ ${timeStr}`;
    }
    formatted += `\n   🎯 ${match.bet_type}: ${match.selection} | 💰 Cotă: ${match.odds}\n\n`;
  });

  formatted += `💎 **Cotă totală: ${ticket.totalOdds}**\n`;
  formatted += `📊 **Încredere AI: ${ticket.confidence}**`;

  if (ticket.explanation) {
    formatted += `\n\n📝 **Explicație:**\n${ticket.explanation}`;
  }

  if (ticket.recommended_stake) {
    formatted += `\n\n💰 **Miză recomandată: ${ticket.recommended_stake} RON**`;
  }

  if (ticket.potential_win) {
    formatted += `\n🎯 **Câștig potențial: ${ticket.potential_win} RON**`;
  }

  if (ticket.risk_level) {
    const riskEmoji = ticket.risk_level === 'ridicat' ? '🔥' : ticket.risk_level === 'mediu' ? '⚖️' : '🛡️';
    formatted += `\n${riskEmoji} **Nivel risc: ${ticket.risk_level}**`;
  }

  return formatted;
}
