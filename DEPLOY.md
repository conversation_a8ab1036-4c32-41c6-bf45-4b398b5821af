# Deploy AIpariat pe Heroku

## 📋 Pa<PERSON>i pentru Deploy

### 1. Pregătire Heroku App
```bash
# Conectează-te la Heroku (dacă nu ești deja)
heroku login

# Conectează repo-ul local la app-ul Heroku
heroku git:remote -a newaipariat
```

### 2. Adaugă PostgreSQL Database
```bash
# Adaugă Heroku Postgres addon
heroku addons:create heroku-postgresql:essential-0 -a newaipariat

# Verifică că DATABASE_URL a fost setat automat
heroku config -a newaipariat
```

### 3. Configurează Environment Variables
```bash
# Setează URL-ul aplicației pentru share links
heroku config:set NEXT_PUBLIC_BASE_URL=https://newaipariat.herokuapp.com -a newaipariat

# Setează environment pentru producție
heroku config:set NODE_ENV=production -a newaipariat

# Optional: Token pentru cleanup (pentru securitate)
heroku config:set CLEANUP_AUTH_TOKEN=your-secret-token -a newaipariat
```

### 4. Deploy Aplicația
```bash
# Adaugă toate fișierele
git add .

# Commit modificările
git commit -m "Prepare for Heroku deploy with PostgreSQL"

# Push pe Heroku
git push heroku main
```

### 5. Verifică Deploy-ul
```bash
# Deschide aplicația
heroku open -a newaipariat

# Verifică logs-urile
heroku logs --tail -a newaipariat
```

## 🔧 Configurare Automată Database

Aplicația va crea automat tabelele necesare la prima rulare:
- `shared_tickets` - pentru biletele partajate
- Index-uri pentru performanță

## 🧹 Cleanup Automat

Pentru a șterge biletele expirate, poți seta un job periodic:

### Opțiunea 1: Heroku Scheduler (Recomandat)
```bash
# Adaugă Heroku Scheduler addon
heroku addons:create scheduler:standard -a newaipariat

# Apoi în dashboard-ul Heroku, adaugă job:
# Command: curl -X POST https://newaipariat.herokuapp.com/api/cleanup -H "Authorization: Bearer your-secret-token"
# Frequency: Daily
```

### Opțiunea 2: Manual
```bash
# Rulează cleanup manual
curl -X POST https://newaipariat.herokuapp.com/api/cleanup \
  -H "Authorization: Bearer your-secret-token"
```

## 📊 Monitoring

### Verifică starea aplicației:
```bash
# Status general
heroku ps -a newaipariat

# Database info
heroku pg:info -a newaipariat

# Logs în timp real
heroku logs --tail -a newaipariat
```

### Endpoints pentru monitoring:
- `GET /api/tickets` - Status API
- `GET /api/cleanup` - Status cleanup
- `GET /api/tickets/[id]` - Test ticket retrieval

## 🚨 Troubleshooting

### Database Connection Issues:
```bash
# Verifică DATABASE_URL
heroku config:get DATABASE_URL -a newaipariat

# Resetează database (ATENȚIE: șterge toate datele)
heroku pg:reset DATABASE_URL -a newaipariat --confirm newaipariat
```

### Build Issues:
```bash
# Verifică build logs
heroku logs --tail -a newaipariat

# Rebuild aplicația
git commit --allow-empty -m "Trigger rebuild"
git push heroku main
```

### Performance Issues:
```bash
# Upgrade la plan mai mare dacă e necesar
heroku ps:scale web=1:standard-1x -a newaipariat
```

## 🔐 Securitate

1. **Database**: Heroku Postgres include SSL automat
2. **Environment Variables**: Toate secretele sunt în config vars
3. **Cleanup Auth**: Token secret pentru endpoint-ul de cleanup

## 📈 Scaling

Pentru trafic mare:
```bash
# Scale web dynos
heroku ps:scale web=2 -a newaipariat

# Upgrade database
heroku addons:upgrade heroku-postgresql:standard-0 -a newaipariat
```

## ✅ Post-Deploy Checklist

- [ ] Aplicația se deschide la https://newaipariat.herokuapp.com
- [ ] Chat-ul funcționează și generează bilete
- [ ] Butoanele predefinite funcționează
- [ ] Biletele se pot partaja (link-uri funcționale)
- [ ] Biletele partajate se deschid corect
- [ ] Database-ul salvează și recuperează biletele
- [ ] Cleanup endpoint răspunde (GET /api/cleanup)

## 🎯 Next Steps

După deploy, poți:
1. Configura domeniu custom
2. Adăuga monitoring cu Heroku Metrics
3. Configura backup-uri database
4. Implementa CDN pentru assets
5. Adăuga analytics
