# AIpariat - Analize AI pentru Pariuri Sportive

AIpariat este o platformă consultativă care folosește inteligența artificială pentru analize avansate în pariurile sportive. Toate casele de pariuri folosesc algoritmi pentru garantarea profitului - am creat AIpariat pentru egalizarea șanselor.

## 🚀 Funcționalități

### ✨ Funcționalități Principale
- **Chat AI Interactiv** - Conversează cu AI-ul pentru bilete personalizate
- **4 Butoane Predefinite**:
  - 🔥 **Cel mai curajos bilet** - Cote mari, risc ridicat
  - 🛡️ **Cel mai sigur bilet** - Cote mici, șanse mari
  - ⭐ **Recomandarea zilei** - Selecția AI pentru astăzi
  - 👑 **Recomandarea săptămânii** - Biletul premium al săptămânii

### 🔗 Sistem de Partajare
- **Link-uri unice** pentru fiecare bilet generat
- **Expirare automată** după 7 zile
- **Partajare ușoară** prin clipboard
- **Pagini dedicate** pentru biletele partajate

### 🎨 Design și UX
- **Temă sportiv-casino-AI** cu elemente moderne
- **Responsive design** optimizat pentru mobile și desktop
- **Mobile-first approach** cu touch targets optimizate
- **Animații și efecte** pentru o experiență premium
- **Culori și gradienți** care sugerează AI și automatizare
- **Typography scalabilă** pentru toate dimensiunile de ecran

## 🛠️ Tehnologii Folosite

- **Next.js 15** - Framework React modern
- **TypeScript** - Tipizare statică
- **Tailwind CSS v4** - Stilizare utilitar
- **React Hooks** - Managementul stării
- **LocalStorage** - Salvarea temporară a biletelor

## 📦 Instalare și Rulare

### Cerințe
- Node.js 18+
- npm sau yarn

### Pași de instalare
```bash
# Navighează în directorul proiectului
cd aipariat

# Instalează dependențele (deja instalate)
npm install

# Pornește serverul de dezvoltare
npm run dev
```

Aplicația va fi disponibilă la `http://localhost:3000`

## 🏗️ Structura Proiectului

```
aipariat/
├── src/
│   ├── app/
│   │   ├── bilet/[id]/     # Pagini pentru biletele partajate
│   │   ├── globals.css     # Stiluri globale și tema
│   │   ├── layout.tsx      # Layout principal
│   │   └── page.tsx        # Pagina principală
│   └── lib/
│       └── ticketGenerator.ts  # Logica de generare bilete
├── public/                 # Fișiere statice
└── package.json           # Dependențe și scripturi
```

## 🎯 Funcționalități Implementate

### ✅ Chat AI
- Răspunsuri simulate realiste
- Analiză query-uri utilizator
- Generare bilete personalizate
- Interfață conversațională

### ✅ Butoane Predefinite
- 4 tipuri distincte de bilete
- Logică specifică pentru fiecare tip
- Cote și predicții realiste
- Echipe românești și internaționale

### ✅ Sistem Partajare
- URL-uri unice pentru fiecare bilet
- Salvare în localStorage
- Expirare automată după 7 zile
- Pagini dedicate pentru vizualizare

### ✅ Design Premium
- Temă dark cu accente neon
- Gradienți și efecte glow
- Animații smooth
- **Mobile-first responsive design**
- Touch targets optimizate pentru mobile
- Typography scalabilă (text-sm/lg/xl)
- Layout adaptat pentru toate dispozitivele

## 🎮 Cum să Folosești Aplicația

1. **Accesează** aplicația la `http://localhost:3000`
2. **Alege** una din opțiuni:
   - Folosește unul din cele 4 butoane predefinite
   - Scrie o întrebare în chat (ex: "Vreau un bilet sigur")
3. **Primești** un bilet generat de AI cu:
   - Meciuri și predicții
   - Cote realiste
   - Cotă totală
   - Nivel de încredere
4. **Partajează** biletul folosind butonul "Partajează biletul"
5. **Link-ul** va fi valid 7 zile

## 📝 Exemple de Descrieri pentru Chat

- "Bilet sigur cu cote mici"
- "Bilet curajos cu cote mari"
- "Meciuri din Liga 1 România"
- "Bilet cu Champions League"
- "Analiză pentru Real Madrid"

## ⚠️ Note Importante

- **Rol consultativ**: Această platformă are exclusiv rol consultativ și nu poate fi folosită pentru plasarea pariurilor sportive
- **Simulare**: Toate biletele sunt generate simulat pentru demonstrație
- **Educațional**: Scopul este demonstrarea tehnologiilor moderne și egalizarea șanselor
- **Expirare**: Link-urile partajate expiră automat după 7 zile

---

**AIpariat** - Unde inteligența artificială întâlnește pasiunea pentru sport! 🚀⚽
