// Script to initialize database tables on Heroku
const { Pool } = require('pg');

async function initializeDatabase() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  });

  try {
    console.log('Connecting to database...');
    
    // Create tickets table if it doesn't exist
    await pool.query(`
      CREATE TABLE IF NOT EXISTS shared_tickets (
        id VARCHAR(255) PRIMARY KEY,
        ticket_data JSONB NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL
      );
    `);

    console.log('✓ Table shared_tickets created/verified');

    // Create index for cleanup
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_shared_tickets_expires_at 
      ON shared_tickets(expires_at);
    `);

    console.log('✓ Index created/verified');

    // Test insert and select
    const testId = `test_${Date.now()}`;
    const testTicket = {
      id: testId,
      type: 'test',
      matches: [],
      totalOdds: 1.0,
      description: 'Test ticket',
      confidence: 100
    };

    await pool.query(
      `INSERT INTO shared_tickets (id, ticket_data, expires_at) 
       VALUES ($1, $2, $3)`,
      [testId, JSON.stringify(testTicket), new Date(Date.now() + 60000)] // 1 minute
    );

    console.log('✓ Test insert successful');

    const result = await pool.query(
      `SELECT id, ticket_data FROM shared_tickets WHERE id = $1`,
      [testId]
    );

    if (result.rows.length > 0) {
      console.log('✓ Test select successful');
      console.log('Retrieved ticket:', result.rows[0].ticket_data);
    }

    // Clean up test data
    await pool.query(`DELETE FROM shared_tickets WHERE id = $1`, [testId]);
    console.log('✓ Test cleanup successful');

    console.log('\n🎉 Database initialization completed successfully!');
    
  } catch (error) {
    console.error('❌ Error initializing database:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the initialization
initializeDatabase();
