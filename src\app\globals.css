@import "tailwindcss";

:root {
  /* AIpariat Theme - Sportiv-Casino-AI */
  --background: #0a0a0a;
  --foreground: #ffffff;
  --primary: #00ff88;
  --primary-dark: #00cc6a;
  --secondary: #ff6b35;
  --accent: #ffd700;
  --casino-red: #dc2626;
  --casino-green: #16a34a;
  --ai-blue: #3b82f6;
  --ai-purple: #8b5cf6;
  --dark-surface: #1a1a1a;
  --darker-surface: #111111;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-dark: var(--primary-dark);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-casino-red: var(--casino-red);
  --color-casino-green: var(--casino-green);
  --color-ai-blue: var(--ai-blue);
  --color-ai-purple: var(--ai-purple);
  --color-dark-surface: var(--dark-surface);
  --color-darker-surface: var(--darker-surface);
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', monospace;
}

body {
  background: linear-gradient(135deg, var(--background) 0%, var(--darker-surface) 100%);
  color: var(--foreground);
  font-family: var(--font-sans);
  min-height: 100vh;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dark-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Glow effects for AI theme */
.glow-primary {
  box-shadow: 0 0 20px rgba(0, 255, 136, 0.3);
}

.glow-ai {
  box-shadow: 0 0 30px rgba(59, 130, 246, 0.4);
}

/* Casino-style animations */
@keyframes pulse-casino {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.pulse-casino {
  animation: pulse-casino 2s infinite;
}

/* Gradient text for AI branding */
.gradient-text {
  background: linear-gradient(45deg, var(--primary), var(--ai-blue), var(--ai-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  /* Improve touch targets */
  button {
    min-height: 44px;
  }

  /* Better text readability on small screens */
  body {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  /* Prevent zoom on input focus */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  textarea {
    font-size: 16px;
  }

  /* Smooth scrolling for mobile */
  * {
    -webkit-overflow-scrolling: touch;
  }
}

/* Improve button interactions on touch devices */
@media (hover: none) and (pointer: coarse) {
  button:hover {
    transform: none;
  }

  button:active {
    transform: scale(0.95);
  }
}
