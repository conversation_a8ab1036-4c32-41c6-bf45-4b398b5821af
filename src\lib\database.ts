// Database configuration and operations for AIpariat
import { Pool } from 'pg';
import { Ticket } from './ticketGenerator';

interface TicketData {
  id: string;
  ticket: Ticket;
  createdAt: string;
  expiresAt: string;
}

// Database connection
let pool: Pool | null = null;

function getPool(): Pool {
  if (!pool) {
    pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    });
  }
  return pool;
}

// Initialize database tables
export async function initializeDatabase(): Promise<void> {
  const client = getPool();
  
  try {
    // Create tickets table if it doesn't exist
    await client.query(`
      CREATE TABLE IF NOT EXISTS shared_tickets (
        id VARCHAR(255) PRIMARY KEY,
        ticket_data JSONB NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL
      );
    `);

    // Create index for cleanup
    await client.query(`
      CREATE INDEX IF NOT EXISTS idx_shared_tickets_expires_at 
      ON shared_tickets(expires_at);
    `);

    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Error initializing database:', error);
    throw error;
  }
}

// Save a ticket to database
export async function saveTicket(ticketId: string, ticket: Ticket): Promise<string> {
  const client = getPool();
  
  try {
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days from now
    
    await client.query(
      `INSERT INTO shared_tickets (id, ticket_data, expires_at) 
       VALUES ($1, $2, $3)`,
      [ticketId, JSON.stringify(ticket), expiresAt]
    );

    return ticketId;
  } catch (error) {
    console.error('Error saving ticket:', error);
    throw error;
  }
}

// Get a ticket from database
export async function getTicket(ticketId: string): Promise<TicketData | null> {
  const client = getPool();
  
  try {
    const result = await client.query(
      `SELECT id, ticket_data, created_at, expires_at 
       FROM shared_tickets 
       WHERE id = $1 AND expires_at > NOW()`,
      [ticketId]
    );

    if (result.rows.length === 0) {
      return null;
    }

    const row = result.rows[0];
    return {
      id: row.id,
      ticket: typeof row.ticket_data === 'string' ? JSON.parse(row.ticket_data) : row.ticket_data,
      createdAt: row.created_at.toISOString(),
      expiresAt: row.expires_at.toISOString(),
    };
  } catch (error) {
    console.error('Error getting ticket:', error);
    throw error;
  }
}

// Clean up expired tickets (called periodically)
export async function cleanupExpiredTickets(): Promise<number> {
  const client = getPool();
  
  try {
    const result = await client.query(
      `DELETE FROM shared_tickets WHERE expires_at <= NOW()`
    );

    console.log(`Cleaned up ${result.rowCount} expired tickets`);
    return result.rowCount || 0;
  } catch (error) {
    console.error('Error cleaning up expired tickets:', error);
    throw error;
  }
}

// Get statistics (for admin/monitoring)
export async function getTicketStats(): Promise<{
  total: number;
  todayCount: number;
  weekCount: number;
}> {
  const client = getPool();
  
  try {
    const totalResult = await client.query(
      `SELECT COUNT(*) as count FROM shared_tickets WHERE expires_at > NOW()`
    );

    const todayResult = await client.query(
      `SELECT COUNT(*) as count FROM shared_tickets 
       WHERE created_at >= CURRENT_DATE AND expires_at > NOW()`
    );

    const weekResult = await client.query(
      `SELECT COUNT(*) as count FROM shared_tickets 
       WHERE created_at >= CURRENT_DATE - INTERVAL '7 days' AND expires_at > NOW()`
    );

    return {
      total: parseInt(totalResult.rows[0].count),
      todayCount: parseInt(todayResult.rows[0].count),
      weekCount: parseInt(weekResult.rows[0].count),
    };
  } catch (error) {
    console.error('Error getting ticket stats:', error);
    throw error;
  }
}

// Close database connection (for cleanup)
export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
  }
}
