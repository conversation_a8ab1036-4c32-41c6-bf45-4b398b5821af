import type { <PERSON>ada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
  display: "swap",
});

const baseUrl = process.env.BASE_URL || 'https://aipariat.com';

export const metadata: Metadata = {
  title: {
    default: "AIpariat - Analize AI pentru Pariuri Sportive",
    template: "%s | AIpariat"
  },
  description: "Toate casele de pariuri folosesc algoritmi pentru garantarea profitului. Am creat AIpariat pentru egalizarea șanselor. Platformă consultativă cu analize AI avansate pentru pariuri sportive.",
  keywords: [
    "pariuri sportive",
    "analize AI",
    "bilete pariuri",
    "predicții fotbal",
    "România",
    "algoritmi pariuri",
    "consultanță sportivă",
    "inteligență artificială",
    "Liga 1",
    "Champions League"
  ],
  authors: [{ name: "AIpariat", url: baseUrl }],
  creator: "AIpariat",
  publisher: "AIpariat",
  metadataBase: new URL(baseUrl),
  alternates: {
    canonical: baseUrl,
  },
  openGraph: {
    type: 'website',
    locale: 'ro_RO',
    url: baseUrl,
    title: 'AIpariat - Analize AI pentru Pariuri Sportive',
    description: 'Toate casele de pariuri folosesc algoritmi pentru garantarea profitului. Am creat AIpariat pentru egalizarea șanselor.',
    siteName: 'AIpariat',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AIpariat - Analize AI pentru Pariuri Sportive',
    description: 'Platformă consultativă cu analize AI avansate pentru pariuri sportive.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "AIpariat",
    "description": "Platformă consultativă cu analize AI avansate pentru pariuri sportive",
    "url": baseUrl,
    "applicationCategory": "SportsApplication",
    "operatingSystem": "Web",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "RON"
    },
    "author": {
      "@type": "Organization",
      "name": "AIpariat"
    },
    "inLanguage": "ro-RO"
  };

  return (
    <html lang="ro">
      <head>
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="dns-prefetch" href="https://aipariatagent-1914c9cd53a9.herokuapp.com" />
        <meta name="theme-color" content="#00d4ff" />
      </head>
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
