// API route for cleaning up expired tickets
import { NextRequest, NextResponse } from 'next/server';
import { cleanupExpiredTickets, initializeDatabase } from '@/lib/database';

// Initialize database on first request
let dbInitialized = false;

async function ensureDbInitialized() {
  if (!dbInitialized) {
    try {
      await initializeDatabase();
      dbInitialized = true;
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }
}

export async function POST(request: NextRequest) {
  try {
    await ensureDbInitialized();

    // Simple authentication check (you can enhance this)
    const authHeader = request.headers.get('authorization');
    const expectedAuth = process.env.CLEANUP_AUTH_TOKEN || 'cleanup-secret';
    
    if (authHeader !== `Bearer ${expectedAuth}`) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const deletedCount = await cleanupExpiredTickets();

    return NextResponse.json({
      success: true,
      deletedCount,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error in cleanup:', error);
    return NextResponse.json(
      { error: 'Cleanup failed' },
      { status: 500 }
    );
  }
}

// Allow GET for health check
export async function GET() {
  return NextResponse.json({
    status: 'Cleanup endpoint is ready',
    timestamp: new Date().toISOString(),
  });
}
